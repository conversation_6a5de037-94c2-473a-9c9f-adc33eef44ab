# Dependency directories
node_modules/
.pnp/
.pnp.js

# Build output
dist/
build/
.next/
out/

# System files
.DS_Store
Thumbs.db

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment variables
.env
.env.*.local

# IDE/editor folders
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Misc
coverage/
.cache/
.eslintcache
parcel-cache/
turbo/
*.log

# Storybook
.storybook-out/
storybook-static/

# Mac-specific
.AppleDouble
.LSOverride

# Test coverage
coverage/

# Docs
docs/discovery/
